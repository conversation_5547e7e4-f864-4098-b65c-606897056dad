using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;

namespace RectangularPanelTool
{
    /// <summary>
    /// 排板方向枚举
    /// </summary>
    public enum PanelDirection
    {
        /// <summary>
        /// 从左往右排版
        /// </summary>
        LeftToRight,

        /// <summary>
        /// 从右往左排版
        /// </summary>
        RightToLeft,

        /// <summary>
        /// 从中间向两边排版
        /// </summary>
        CenterToSides
    }

    /// <summary>
    /// 参数验证异常
    /// </summary>
    public class InvalidParameterException : Exception
    {
        public InvalidParameterException(string message) : base(message) { }
        public InvalidParameterException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// 几何计算异常
    /// </summary>
    public class GeometryCalculationException : Exception
    {
        public GeometryCalculationException(string message) : base(message) { }
        public GeometryCalculationException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class PanelGenerator
    {
        #region 常量定义
        private const double TOLERANCE = 1e-9;
        private const double MIN_PANEL_HEIGHT = 1.0;
        private const int MAX_ITERATIONS = 10000;
        private const double MIN_PANEL_WIDTH = 0.1;
        #endregion

        #region 私有字段
        private Document _doc;
        private Editor _ed;
        private Database _db;
        private ObjectId _outlineId;
        private List<ObjectId> _windowIds;
        private List<ObjectId> _doorIds;
        private double _startDistance;
        private double _panelWidth;
        private double _topGap;
        private double _bottomGap;
        private double _windowGap;
        private double _doorTopGap;
        private PanelDirection _direction;
        #endregion

        #region 属性
        public bool HasOutline => !_outlineId.IsNull;
        public int WindowCount => _windowIds?.Count ?? 0;
        public int DoorCount => _doorIds?.Count ?? 0;

        public double StartDistance
        {
            get => _startDistance;
            set => _startDistance = value;
        }

        public double PanelWidth
        {
            get => _panelWidth;
            set
            {
                if (value <= MIN_PANEL_WIDTH)
                    throw new InvalidParameterException($"板宽必须大于{MIN_PANEL_WIDTH}");
                _panelWidth = value;
            }
        }

        public double TopGap
        {
            get => _topGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("顶部间隙不能为负数");
                _topGap = value;
            }
        }

        public double BottomGap
        {
            get => _bottomGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("底部间隙不能为负数");
                _bottomGap = value;
            }
        }

        public double WindowGap
        {
            get => _windowGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("窗户间隙不能为负数");
                _windowGap = value;
            }
        }

        public double DoorTopGap
        {
            get => _doorTopGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("门顶部间隙不能为负数");
                _doorTopGap = value;
            }
        }

        public PanelDirection Direction { get => _direction; set => _direction = value; }
        #endregion

        #region 构造函数
        public PanelGenerator(Document doc)
        {
            _doc = doc ?? throw new ArgumentNullException(nameof(doc));
            _ed = doc.Editor;
            _db = doc.Database;
            _outlineId = ObjectId.Null;
            _windowIds = new List<ObjectId>();
            _doorIds = new List<ObjectId>();

            // 设置默认值
            _panelWidth = 1000;
            _topGap = 5;
            _bottomGap = 10;
            _windowGap = 5;
            _doorTopGap = 5;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 生成矩形板
        /// </summary>
        /// <returns>成功生成的矩形板数量</returns>
        public int GeneratePanels()
        {
            ValidateParameters();

            if (_outlineId.IsNull)
            {
                _ed.WriteMessage("\n请先选择轮廓线!");
                return 0;
            }

            // 对于中心向两边排版，需要先获取中心点
            Point3d? centerPoint = null;
            if (_direction == PanelDirection.CenterToSides)
            {
                PromptPointResult result = _ed.GetPoint("\n请选择中心点: ");
                if (result.Status != PromptStatus.OK)
                {
                    return 0;
                }
                centerPoint = result.Value;
            }

            int panelCount = 0;

            using (Transaction tr = _db.TransactionManager.StartTransaction())
            {
                try
                {
                    // 获取轮廓线
                    Polyline outline = tr.GetObject(_outlineId, OpenMode.ForRead) as Polyline;
                    if (outline == null)
                    {
                        _ed.WriteMessage("\n无法打开轮廓线!");
                        tr.Commit();
                        return 0;
                    }

                    // 如果轮廓线不是闭合的，则创建一个临时的闭合副本
                    Polyline workingOutline = outline;
                    bool needsDisposal = false;
                    if (!outline.Closed)
                    {
                        _ed.WriteMessage("\n自动闭合轮廓线进行处理...");
                        workingOutline = outline.Clone() as Polyline;
                        workingOutline.Closed = true;
                        needsDisposal = true;
                    }

                    try
                    {
                        // 获取轮廓线的边界盒，仅用于确定X方向范围
                        Extents3d outlineExtents = workingOutline.GeometricExtents;
                        double minX = outlineExtents.MinPoint.X;
                        double maxX = outlineExtents.MaxPoint.X;

                        // 加载门窗洞口
                        List<Extents3d> windowExtents = LoadWindowExtents(tr);
                        List<Extents3d> doorExtents = LoadDoorExtents(tr);

                        // 创建块表记录
                        BlockTable bt = tr.GetObject(_db.BlockTableId, OpenMode.ForRead) as BlockTable;
                        BlockTableRecord ms = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                        // 根据不同排版方向生成板子
                        switch (_direction)
                        {
                            case PanelDirection.LeftToRight:
                                panelCount += GenerateLeftToRightPanels(workingOutline, minX, maxX, windowExtents, doorExtents, ms, tr);
                                break;
                            case PanelDirection.RightToLeft:
                                panelCount += GenerateRightToLeftPanels(workingOutline, minX, maxX, windowExtents, doorExtents, ms, tr);
                                break;
                            case PanelDirection.CenterToSides:
                                if (centerPoint.HasValue)
                                {
                                    panelCount += GenerateCenterToSidesPanels(workingOutline, minX, maxX, windowExtents, doorExtents, ms, tr, centerPoint.Value.X);
                                }
                                break;
                        }
                    }
                    finally
                    {
                        // 如果创建了临时副本，应当释放资源
                        if (needsDisposal && workingOutline != null)
                        {
                            workingOutline.Dispose();
                        }
                    }

                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    _ed.WriteMessage("\n生成矩形板时出错: {0}", ex.Message);
                    tr.Abort();
                    return 0;
                }
            }

            return panelCount;
        }

        /// <summary>
        /// 验证参数有效性
        /// </summary>
        private void ValidateParameters()
        {
            if (_panelWidth <= MIN_PANEL_WIDTH)
                throw new InvalidParameterException($"板宽必须大于{MIN_PANEL_WIDTH}");

            if (_topGap < 0 || _bottomGap < 0 || _windowGap < 0 || _doorTopGap < 0)
                throw new InvalidParameterException("间隙值不能为负数");
        }

        /// <summary>
        /// 加载窗洞口范围
        /// </summary>
        private List<Extents3d> LoadWindowExtents(Transaction tr)
        {
            List<Extents3d> windowExtents = new List<Extents3d>();
            foreach (ObjectId windowId in _windowIds)
            {
                if (!windowId.IsNull)
                {
                    Polyline window = tr.GetObject(windowId, OpenMode.ForRead) as Polyline;
                    if (window != null && window.Closed)
                    {
                        windowExtents.Add(window.GeometricExtents);
                    }
                }
            }
            return windowExtents;
        }

        /// <summary>
        /// 加载门洞口范围
        /// </summary>
        private List<Extents3d> LoadDoorExtents(Transaction tr)
        {
            List<Extents3d> doorExtents = new List<Extents3d>();
            foreach (ObjectId doorId in _doorIds)
            {
                if (!doorId.IsNull)
                {
                    Polyline door = tr.GetObject(doorId, OpenMode.ForRead) as Polyline;
                    if (door != null && !door.Closed)
                    {
                        doorExtents.Add(door.GeometricExtents);
                    }
                }
            }
            return doorExtents;
        }
        #endregion

        #region 私有排版方法
        /// <summary>
        /// 从左往右排版
        /// </summary>
        private int GenerateLeftToRightPanels(Polyline outline, double minX, double maxX,
            List<Extents3d> windowExtents, List<Extents3d> doorExtents,
            BlockTableRecord ms, Transaction tr)
        {
            int panelCount = 0;
            int iterations = 0;

            // 确定起始点和结束点
            double startX = minX + _startDistance;
            double endX = maxX;

            // 计算当前X位置
            double currentX = startX;

            // 开始排版直到填满整个轮廓，添加循环保护
            while ((currentX < endX || (_startDistance < 0 && currentX < minX)) && iterations < MAX_ITERATIONS)
            {
                double nextX = currentX + _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(outline, currentX, nextX, windowExtents, doorExtents, ms, tr);

                // 更新当前X位置
                currentX = nextX;
                iterations++;

                // 如果起始距离为正且当前位置已超出结束点，则停止排版
                if (currentX >= endX && _startDistance >= 0)
                {
                    break;
                }
            }

            if (iterations >= MAX_ITERATIONS)
            {
                _ed.WriteMessage($"\n警告：达到最大迭代次数{MAX_ITERATIONS}，排版可能不完整");
            }

            return panelCount;
        }
        
        /// <summary>
        /// 从右往左排版
        /// </summary>
        private int GenerateRightToLeftPanels(Polyline outline, double minX, double maxX,
            List<Extents3d> windowExtents, List<Extents3d> doorExtents,
            BlockTableRecord ms, Transaction tr)
        {
            int panelCount = 0;
            int iterations = 0;

            // 确定起始点
            double startX = maxX - _startDistance;
            double endX = minX;

            // 计算当前X位置
            double currentX = startX;

            // 开始排版直到填满整个轮廓，添加循环保护
            while ((currentX > endX || (_startDistance < 0 && currentX > maxX)) && iterations < MAX_ITERATIONS)
            {
                double nextX = currentX - _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(outline, currentX, nextX, windowExtents, doorExtents, ms, tr);

                // 更新当前X位置
                currentX = nextX;
                iterations++;

                // 如果起始距离为正且当前位置已超出结束点，则停止排版
                if (currentX <= endX && _startDistance >= 0)
                {
                    break;
                }
            }

            if (iterations >= MAX_ITERATIONS)
            {
                _ed.WriteMessage($"\n警告：达到最大迭代次数{MAX_ITERATIONS}，排版可能不完整");
            }

            return panelCount;
        }
        
        /// <summary>
        /// 从中间向两边排版
        /// </summary>
        private int GenerateCenterToSidesPanels(Polyline outline, double minX, double maxX,
            List<Extents3d> windowExtents, List<Extents3d> doorExtents,
            BlockTableRecord ms, Transaction tr, double centerX)
        {
            int panelCount = 0;
            int leftIterations = 0;
            int rightIterations = 0;

            // 确定起始位置
            double leftStartX, rightStartX;

            if (_startDistance >= 0)
            {
                // 正常起始距离，向内缩进
                leftStartX = centerX - _startDistance / 2;
                rightStartX = centerX + _startDistance / 2;
            }
            else
            {
                // 负值起始距离，向外扩展
                leftStartX = centerX + _startDistance / 2;  // 向左扩展（负值的一半）
                rightStartX = centerX - _startDistance / 2; // 向右扩展（负值的一半变为正值）
            }

            // 设置当前位置为起始位置
            double currentLeftX = leftStartX;
            double currentRightX = rightStartX;

            // 向左生成板子，添加循环保护
            while ((currentLeftX > minX || (_startDistance < 0 && currentLeftX > centerX + _startDistance)) && leftIterations < MAX_ITERATIONS)
            {
                // 计算下一个位置，保持完整板宽
                double nextLeftX = currentLeftX - _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(outline, currentLeftX, nextLeftX, windowExtents, doorExtents, ms, tr);

                // 更新当前X位置
                currentLeftX = nextLeftX;
                leftIterations++;

                // 如果起始距离为正且当前位置已超出边界，则停止排版
                if (currentLeftX <= minX && _startDistance >= 0)
                {
                    break;
                }
            }

            // 向右生成板子，添加循环保护
            while ((currentRightX < maxX || (_startDistance < 0 && currentRightX < centerX - _startDistance)) && rightIterations < MAX_ITERATIONS)
            {
                // 计算下一个位置，保持完整板宽
                double nextRightX = currentRightX + _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(outline, currentRightX, nextRightX, windowExtents, doorExtents, ms, tr);

                // 更新当前X位置
                currentRightX = nextRightX;
                rightIterations++;

                // 如果起始距离为正且当前位置已超出边界，则停止排版
                if (currentRightX >= maxX && _startDistance >= 0)
                {
                    break;
                }
            }

            if (leftIterations >= MAX_ITERATIONS || rightIterations >= MAX_ITERATIONS)
            {
                _ed.WriteMessage($"\n警告：达到最大迭代次数{MAX_ITERATIONS}，排版可能不完整");
            }

            return panelCount;
        }
        
        /// <summary>
        /// 在指定位置生成板子
        /// </summary>
        private int GeneratePanelAtPosition(Polyline outline, double minX, double maxX,
            List<Extents3d> windowExtents, List<Extents3d> doorExtents,
            BlockTableRecord ms, Transaction tr)
        {
            int panelCount = 0;

            double panelMinX = Math.Min(minX, maxX);
            double panelMaxX = Math.Max(minX, maxX);

            // 计算当前X位置下，轮廓线的实际最高和最低点
            Tuple<double, double> yRange = GetOutlineYRangeAtXrange(outline, panelMinX, panelMaxX);
            if (yRange == null)
            {
                // 如果在此X位置没有有效的Y范围，则跳过
                return 0;
            }

            // 应用间隙
            double minY = yRange.Item1 + _bottomGap;

            // 修改：分别获取左右两侧边缘的Y坐标，取较小值确保矩形板顶部不超出轮廓线
            // 左侧边缘Y坐标
            double leftEdgeY = GetYCoordinateAtX(outline, panelMinX);
            // 右侧边缘Y坐标
            double rightEdgeY = GetYCoordinateAtX(outline, panelMaxX);

            // 检查是否获取到有效的Y坐标
            if (Math.Abs(leftEdgeY - double.MinValue) < TOLERANCE || Math.Abs(rightEdgeY - double.MinValue) < TOLERANCE)
            {
                // 如果无法获取有效的Y坐标，跳过此位置
                return 0;
            }

            // 取两者的较小值作为矩形板顶部的Y坐标
            double topY = Math.Min(leftEdgeY, rightEdgeY) - _topGap;

            // 性能优化：只处理与当前板子X范围相交的洞口
            List<Extents3d> relevantWindows = GetIntersectingHoles(windowExtents, panelMinX, panelMaxX);
            List<Extents3d> relevantDoors = GetIntersectingHoles(doorExtents, panelMinX, panelMaxX);

            // 使用新的智能避障算法生成板材形状
            List<Point2d> panelShape = GenerateSmartPanelShape(panelMinX, panelMaxX, minY, topY,
                relevantWindows, relevantDoors);

            // 如果无法生成有效形状，回退到传统方法
            if (panelShape == null || panelShape.Count < 3)
            {
                // 使用传统的Y范围分割方法作为备选方案
                panelCount += GeneratePanelUsingTraditionalMethod(panelMinX, panelMaxX, minY, topY,
                    relevantWindows, relevantDoors, ms, tr);
            }
            else
            {
                // 使用智能生成的形状创建板材
                try
                {
                    using (Polyline panel = new Polyline())
                    {
                        for (int i = 0; i < panelShape.Count; i++)
                        {
                            panel.AddVertexAt(i, panelShape[i], 0, 0, 0);
                        }
                        panel.Closed = true;

                        // 验证生成的形状是否有效（面积足够大）
                        double area = CalculatePolygonArea(panelShape);
                        if (area > MIN_PANEL_HEIGHT * MIN_PANEL_WIDTH)
                        {
                            // 将板添加到模型空间
                            ms.AppendEntity(panel);
                            tr.AddNewlyCreatedDBObject(panel, true);
                            panelCount++;
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    _ed.WriteMessage($"\n创建智能板材时出错: {ex.Message}");
                    // 出错时回退到传统方法
                    panelCount += GeneratePanelUsingTraditionalMethod(panelMinX, panelMaxX, minY, topY,
                        relevantWindows, relevantDoors, ms, tr);
                }
            }

            return panelCount;
        }

        /// <summary>
        /// 测试智能避障算法的功能
        /// </summary>
        /// <returns>测试结果描述</returns>
        public string TestSmartAvoidanceAlgorithm()
        {
            try
            {
                _ed.WriteMessage("\n开始测试智能门窗避障算法...");

                // 测试用例1：板材与窗户部分重叠
                List<Extents3d> testWindows = new List<Extents3d>
                {
                    new Extents3d(new Point3d(50, 100, 0), new Point3d(150, 200, 0))
                };

                List<Extents3d> testDoors = new List<Extents3d>();

                // 测试板材：与窗户部分重叠
                var shape1 = GenerateSmartPanelShape(0, 100, 50, 250, testWindows, testDoors);
                string result1 = shape1 != null ? $"成功生成{shape1.Count}个顶点的避障形状" : "无法生成避障形状";

                // 测试用例2：板材完全包含窗户
                var shape2 = GenerateSmartPanelShape(0, 200, 50, 250, testWindows, testDoors);
                string result2 = shape2 != null ? $"成功生成{shape2.Count}个顶点的包含形状" : "无法生成包含形状";

                // 测试用例3：板材与门部分重叠
                List<Extents3d> testDoors2 = new List<Extents3d>
                {
                    new Extents3d(new Point3d(50, 0, 0), new Point3d(150, 220, 0))
                };

                var shape3 = GenerateSmartPanelShape(0, 100, 50, 250, new List<Extents3d>(), testDoors2);
                string result3 = shape3 != null ? $"成功生成{shape3.Count}个顶点的门避障形状" : "无法生成门避障形状";

                string testResult = $"\n智能避障算法测试结果:\n" +
                                  $"- 窗户部分重叠测试: {result1}\n" +
                                  $"- 窗户完全包含测试: {result2}\n" +
                                  $"- 门部分重叠测试: {result3}";

                _ed.WriteMessage(testResult);
                return testResult;
            }
            catch (Exception ex)
            {
                string errorMsg = $"\n测试智能避障算法时出错: {ex.Message}";
                _ed.WriteMessage(errorMsg);
                return errorMsg;
            }
        }
        #endregion

        /// <summary>
        /// 获取轮廓线在指定X范围内的Y范围(最高点和最低点)
        /// </summary>
        private Tuple<double, double> GetOutlineYRangeAtXrange(Polyline outline, double minX, double maxX)
        {
            double minY = double.MaxValue;
            double maxY = double.MinValue;
            bool foundIntersection = false;

            // 检查每个线段与给定X范围的交点
            for (int i = 0; i < outline.NumberOfVertices; i++)
            {
                int nextIdx = (i + 1) % outline.NumberOfVertices;
                Point2d p1 = outline.GetPoint2dAt(i);
                Point2d p2 = outline.GetPoint2dAt(nextIdx);

                // 判断线段是否在X范围内
                if ((p1.X >= minX && p1.X <= maxX) || (p2.X >= minX && p2.X <= maxX) ||
                    (p1.X <= minX && p2.X >= maxX) || (p1.X >= minX && p2.X <= maxX))
                {
                    // 如果线段端点在X范围内，更新Y范围
                    if (p1.X >= minX && p1.X <= maxX)
                    {
                        minY = Math.Min(minY, p1.Y);
                        maxY = Math.Max(maxY, p1.Y);
                        foundIntersection = true;
                    }
                    
                    if (p2.X >= minX && p2.X <= maxX)
                    {
                        minY = Math.Min(minY, p2.Y);
                        maxY = Math.Max(maxY, p2.Y);
                        foundIntersection = true;
                    }

                    // 如果线段与X范围的边界相交，计算交点的Y值
                    if ((p1.X < minX && p2.X > minX) || (p1.X > minX && p2.X < minX))
                    {
                        // 计算与minX的交点
                        double t = (minX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        minY = Math.Min(minY, y);
                        maxY = Math.Max(maxY, y);
                        foundIntersection = true;
                    }
                    
                    if ((p1.X < maxX && p2.X > maxX) || (p1.X > maxX && p2.X < maxX))
                    {
                        // 计算与maxX的交点
                        double t = (maxX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        minY = Math.Min(minY, y);
                        maxY = Math.Max(maxY, y);
                        foundIntersection = true;
                    }
                }
            }

            // 如果有交点，返回Y范围
            if (foundIntersection)
            {
                return new Tuple<double, double>(minY, maxY);
            }
            
            // 如果没有交点，检查X范围是否在轮廓内部
            Point3d testPoint = new Point3d((minX + maxX) / 2, outline.GeometricExtents.MinPoint.Y, 0);
            if (IsPointInOutline(testPoint, outline))
            {
                // 如果中点在轮廓内，寻找轮廓上方和下方的交点
                double top = double.MinValue;
                double bottom = double.MaxValue;
                
                for (int i = 0; i < outline.NumberOfVertices; i++)
                {
                    int nextIdx = (i + 1) % outline.NumberOfVertices;
                    Point2d p1 = outline.GetPoint2dAt(i);
                    Point2d p2 = outline.GetPoint2dAt(nextIdx);
                    
                    // 检查线段是否跨越测试点的X坐标
                    if ((p1.X <= testPoint.X && p2.X >= testPoint.X) || 
                        (p1.X >= testPoint.X && p2.X <= testPoint.X))
                    {
                        // 计算线段在测试点X坐标处的Y值
                        if (p1.X == p2.X)
                        {
                            // 垂直线段
                            bottom = Math.Min(bottom, Math.Min(p1.Y, p2.Y));
                            top = Math.Max(top, Math.Max(p1.Y, p2.Y));
                        }
                        else
                        {
                            double t = (testPoint.X - p1.X) / (p2.X - p1.X);
                            double y = p1.Y + t * (p2.Y - p1.Y);
                            
                            // 如果线段在测试点上方，更新顶部值
                            if (y > testPoint.Y && y < bottom)
                            {
                                bottom = y;
                            }
                            // 如果线段在测试点下方，更新底部值
                            else if (y < testPoint.Y && y > top)
                            {
                                top = y;
                            }
                        }
                    }
                }
                
                if (top != double.MinValue && bottom != double.MaxValue)
                {
                    return new Tuple<double, double>(top, bottom);
                }
            }
            
            return null;
        }

        #region 几何计算辅助方法
        /// <summary>
        /// 检查点是否在轮廓内
        /// </summary>
        private bool IsPointInOutline(Point3d point, Polyline outline)
        {
            try
            {
                // 使用射线法检查点是否在多边形内部
                // 从该点向右发射一条射线，计算与多边形边的交点数量
                // 如果交点数为奇数，则点在多边形内部；如果为偶数，则点在外部

                int intersections = 0;

                for (int i = 0; i < outline.NumberOfVertices; i++)
                {
                    // 获取当前线段的两个端点
                    int nextIndex = (i + 1) % outline.NumberOfVertices;
                    Point2d pt1 = outline.GetPoint2dAt(i);
                    Point2d pt2 = outline.GetPoint2dAt(nextIndex);

                    // 避免处理水平线段（可能导致精度问题）
                    if (Math.Abs(pt1.Y - pt2.Y) < TOLERANCE)
                        continue;

                    // 判断射线是否与线段相交
                    if ((pt1.Y > point.Y) != (pt2.Y > point.Y))
                    {
                        // 计算交点的X坐标
                        double intersectionX = (pt2.X - pt1.X) * (point.Y - pt1.Y) / (pt2.Y - pt1.Y) + pt1.X;

                        // 只计算在射线右侧的交点
                        if (point.X < intersectionX - TOLERANCE)
                        {
                            intersections++;
                        }
                    }
                }

                // 奇数次交叉表示点在多边形内部
                return (intersections % 2 == 1);
            }
            catch (Exception ex)
            {
                throw new GeometryCalculationException("检查点是否在轮廓内时发生错误", ex);
            }
        }

        /// <summary>
        /// 判断两个X范围是否重叠
        /// </summary>
        private bool IsOverlappingHorizontally(double rect1MinX, double rect1MaxX, double rect2MinX, double rect2MaxX)
        {
            return (rect1MinX <= rect2MaxX && rect1MaxX >= rect2MinX);
        }

        /// <summary>
        /// 分割Y范围，避开洞口
        /// </summary>
        private void SplitYRanges(ref List<Tuple<double, double>> ranges, double yMin, double yMax)
        {
            if (ranges == null)
            {
                ranges = new List<Tuple<double, double>>();
                return;
            }

            // 确保yMin <= yMax
            if (yMin > yMax)
            {
                double temp = yMin;
                yMin = yMax;
                yMax = temp;
            }

            List<Tuple<double, double>> newRanges = new List<Tuple<double, double>>();

            foreach (var range in ranges)
            {
                if (range == null)
                    continue;

                double rangeMin = range.Item1;
                double rangeMax = range.Item2;

                // 确保范围有效
                if (rangeMax <= rangeMin + TOLERANCE)
                    continue;

                // 洞口在范围上方（使用容差比较）
                if (yMin >= rangeMax - TOLERANCE)
                {
                    newRanges.Add(range);
                    continue;
                }

                // 洞口在范围下方（使用容差比较）
                if (yMax <= rangeMin + TOLERANCE)
                {
                    newRanges.Add(range);
                    continue;
                }

                // 洞口与范围有重叠部分
                // 添加洞口下方的范围
                if (rangeMin < yMin - TOLERANCE)
                {
                    double bottomRangeTop = Math.Min(yMin, rangeMax);
                    if (bottomRangeTop - rangeMin > MIN_PANEL_HEIGHT)
                    {
                        newRanges.Add(new Tuple<double, double>(rangeMin, bottomRangeTop));
                    }
                }

                // 添加洞口上方的范围
                if (rangeMax > yMax + TOLERANCE)
                {
                    double topRangeBottom = Math.Max(yMax, rangeMin);
                    if (rangeMax - topRangeBottom > MIN_PANEL_HEIGHT)
                    {
                        newRanges.Add(new Tuple<double, double>(topRangeBottom, rangeMax));
                    }
                }
            }

            ranges = newRanges;
        }

        /// <summary>
        /// 判断矩形是否完全在水平方向上落在洞口范围内
        /// </summary>
        private bool IsRectCompletelyInsideHorizontally(double rectMinX, double rectMaxX, double holeMinX, double holeMaxX)
        {
            // 使用容差比较避免浮点精度问题
            return (rectMinX >= holeMinX - TOLERANCE) && (rectMaxX <= holeMaxX + TOLERANCE);
        }

        /// <summary>
        /// 几何交集类型枚举
        /// </summary>
        private enum IntersectionType
        {
            None,           // 无交集
            Complete,       // 板材完全在洞口内
            Partial,        // 部分交集
            Contains        // 板材包含洞口
        }

        /// <summary>
        /// 检测矩形板材与门窗洞口的交集类型
        /// </summary>
        /// <param name="panelMinX">板材最小X坐标</param>
        /// <param name="panelMaxX">板材最大X坐标</param>
        /// <param name="panelMinY">板材最小Y坐标</param>
        /// <param name="panelMaxY">板材最大Y坐标</param>
        /// <param name="holeMinX">洞口最小X坐标</param>
        /// <param name="holeMaxX">洞口最大X坐标</param>
        /// <param name="holeMinY">洞口最小Y坐标</param>
        /// <param name="holeMaxY">洞口最大Y坐标</param>
        /// <returns>交集类型</returns>
        private IntersectionType GetIntersectionType(double panelMinX, double panelMaxX, double panelMinY, double panelMaxY,
            double holeMinX, double holeMaxX, double holeMinY, double holeMaxY)
        {
            // 检查是否有交集
            if (panelMaxX <= holeMinX + TOLERANCE || panelMinX >= holeMaxX - TOLERANCE ||
                panelMaxY <= holeMinY + TOLERANCE || panelMinY >= holeMaxY - TOLERANCE)
            {
                return IntersectionType.None;
            }

            // 检查板材是否完全在洞口内
            if (panelMinX >= holeMinX - TOLERANCE && panelMaxX <= holeMaxX + TOLERANCE &&
                panelMinY >= holeMinY - TOLERANCE && panelMaxY <= holeMaxY + TOLERANCE)
            {
                return IntersectionType.Complete;
            }

            // 检查板材是否包含洞口
            if (holeMinX >= panelMinX - TOLERANCE && holeMaxX <= panelMaxX + TOLERANCE &&
                holeMinY >= panelMinY - TOLERANCE && holeMaxY <= panelMaxY + TOLERANCE)
            {
                return IntersectionType.Contains;
            }

            // 其他情况为部分交集
            return IntersectionType.Partial;
        }

        /// <summary>
        /// 生成避开门窗的智能板材形状
        /// </summary>
        /// <param name="panelMinX">板材最小X坐标</param>
        /// <param name="panelMaxX">板材最大X坐标</param>
        /// <param name="panelMinY">板材最小Y坐标</param>
        /// <param name="panelMaxY">板材最大Y坐标</param>
        /// <param name="windowExtents">窗户范围列表</param>
        /// <param name="doorExtents">门范围列表</param>
        /// <returns>生成的板材形状点列表，如果无法生成则返回null</returns>
        private List<Point2d> GenerateSmartPanelShape(double panelMinX, double panelMaxX, double panelMinY, double panelMaxY,
            List<Extents3d> windowExtents, List<Extents3d> doorExtents)
        {
            // 基础矩形的四个角点
            List<Point2d> basePoints = new List<Point2d>
            {
                new Point2d(panelMinX, panelMinY),  // 左下
                new Point2d(panelMaxX, panelMinY),  // 右下
                new Point2d(panelMaxX, panelMaxY),  // 右上
                new Point2d(panelMinX, panelMaxY)   // 左上
            };

            // 收集所有需要避开的洞口
            List<Extents3d> allHoles = new List<Extents3d>();

            // 添加相关的窗户（加上间隙）
            foreach (var window in windowExtents)
            {
                var intersectionType = GetIntersectionType(panelMinX, panelMaxX, panelMinY, panelMaxY,
                    window.MinPoint.X, window.MaxPoint.X, window.MinPoint.Y, window.MaxPoint.Y);

                if (intersectionType != IntersectionType.None)
                {
                    // 为窗户添加间隙
                    var expandedWindow = new Extents3d(
                        new Point3d(window.MinPoint.X - TOLERANCE, window.MinPoint.Y - _windowGap, 0),
                        new Point3d(window.MaxPoint.X + TOLERANCE, window.MaxPoint.Y + _windowGap, 0)
                    );
                    allHoles.Add(expandedWindow);
                }
            }

            // 添加相关的门（只有顶部间隙）
            foreach (var door in doorExtents)
            {
                var intersectionType = GetIntersectionType(panelMinX, panelMaxX, panelMinY, panelMaxY,
                    door.MinPoint.X, door.MaxPoint.X, door.MinPoint.Y, door.MaxPoint.Y);

                if (intersectionType != IntersectionType.None)
                {
                    // 为门添加顶部间隙，底部保持原样
                    var expandedDoor = new Extents3d(
                        new Point3d(door.MinPoint.X - TOLERANCE, door.MinPoint.Y, 0),
                        new Point3d(door.MaxPoint.X + TOLERANCE, door.MaxPoint.Y + _doorTopGap, 0)
                    );
                    allHoles.Add(expandedDoor);
                }
            }

            // 如果没有洞口需要避开，返回基础矩形
            if (allHoles.Count == 0)
            {
                return basePoints;
            }

            // 生成避障形状
            return GenerateAvoidanceShape(basePoints, allHoles, panelMinX, panelMaxX, panelMinY, panelMaxY);
        }

        /// <summary>
        /// 生成避开洞口的复杂形状
        /// </summary>
        /// <param name="basePoints">基础矩形点列表</param>
        /// <param name="holes">需要避开的洞口列表</param>
        /// <param name="panelMinX">板材最小X坐标</param>
        /// <param name="panelMaxX">板材最大X坐标</param>
        /// <param name="panelMinY">板材最小Y坐标</param>
        /// <param name="panelMaxY">板材最大Y坐标</param>
        /// <returns>避障形状的点列表</returns>
        private List<Point2d> GenerateAvoidanceShape(List<Point2d> basePoints, List<Extents3d> holes,
            double panelMinX, double panelMaxX, double panelMinY, double panelMaxY)
        {
            // 对于复杂的避障形状生成，我们使用分段方法
            // 将板材分成多个不重叠的矩形区域

            List<List<Point2d>> validRegions = new List<List<Point2d>>();

            // 首先尝试简单的垂直分割方法
            var verticalRegions = GenerateVerticalSplitRegions(panelMinX, panelMaxX, panelMinY, panelMaxY, holes);

            if (verticalRegions != null && verticalRegions.Count > 0)
            {
                validRegions.AddRange(verticalRegions);
            }
            else
            {
                // 如果垂直分割失败，尝试水平分割
                var horizontalRegions = GenerateHorizontalSplitRegions(panelMinX, panelMaxX, panelMinY, panelMaxY, holes);
                if (horizontalRegions != null && horizontalRegions.Count > 0)
                {
                    validRegions.AddRange(horizontalRegions);
                }
            }

            // 如果分割方法都失败，返回null表示无法生成有效形状
            if (validRegions.Count == 0)
            {
                return null;
            }

            // 选择面积最大的区域作为主要形状
            List<Point2d> bestRegion = null;
            double maxArea = 0;

            foreach (var region in validRegions)
            {
                double area = CalculatePolygonArea(region);
                if (area > maxArea && area > MIN_PANEL_HEIGHT * MIN_PANEL_WIDTH)
                {
                    maxArea = area;
                    bestRegion = region;
                }
            }

            return bestRegion;
        }

        /// <summary>
        /// 使用垂直分割方法生成避障区域
        /// </summary>
        private List<List<Point2d>> GenerateVerticalSplitRegions(double panelMinX, double panelMaxX,
            double panelMinY, double panelMaxY, List<Extents3d> holes)
        {
            List<List<Point2d>> regions = new List<List<Point2d>>();

            // 收集所有垂直分割点
            List<double> splitPoints = new List<double> { panelMinX, panelMaxX };

            foreach (var hole in holes)
            {
                // 检查洞口是否与板材有交集
                if (hole.MaxPoint.X > panelMinX + TOLERANCE && hole.MinPoint.X < panelMaxX - TOLERANCE &&
                    hole.MaxPoint.Y > panelMinY + TOLERANCE && hole.MinPoint.Y < panelMaxY - TOLERANCE)
                {
                    if (hole.MinPoint.X > panelMinX + TOLERANCE && hole.MinPoint.X < panelMaxX - TOLERANCE)
                        splitPoints.Add(hole.MinPoint.X);
                    if (hole.MaxPoint.X > panelMinX + TOLERANCE && hole.MaxPoint.X < panelMaxX - TOLERANCE)
                        splitPoints.Add(hole.MaxPoint.X);
                }
            }

            // 排序并去重
            splitPoints.Sort();
            splitPoints = splitPoints.Distinct().ToList();

            // 生成垂直条带区域
            for (int i = 0; i < splitPoints.Count - 1; i++)
            {
                double stripMinX = splitPoints[i];
                double stripMaxX = splitPoints[i + 1];

                if (stripMaxX - stripMinX < MIN_PANEL_WIDTH)
                    continue;

                // 检查这个条带是否与任何洞口冲突
                bool hasConflict = false;
                foreach (var hole in holes)
                {
                    if (IsStripConflictWithHole(stripMinX, stripMaxX, panelMinY, panelMaxY, hole))
                    {
                        hasConflict = true;
                        break;
                    }
                }

                if (!hasConflict)
                {
                    // 创建有效的矩形区域
                    List<Point2d> region = new List<Point2d>
                    {
                        new Point2d(stripMinX, panelMinY),
                        new Point2d(stripMaxX, panelMinY),
                        new Point2d(stripMaxX, panelMaxY),
                        new Point2d(stripMinX, panelMaxY)
                    };
                    regions.Add(region);
                }
                else
                {
                    // 如果有冲突，尝试生成L形或其他复杂形状
                    var complexRegions = GenerateComplexRegionForStrip(stripMinX, stripMaxX, panelMinY, panelMaxY, holes);
                    if (complexRegions != null)
                    {
                        regions.AddRange(complexRegions);
                    }
                }
            }

            return regions;
        }

        /// <summary>
        /// 使用水平分割方法生成避障区域
        /// </summary>
        private List<List<Point2d>> GenerateHorizontalSplitRegions(double panelMinX, double panelMaxX,
            double panelMinY, double panelMaxY, List<Extents3d> holes)
        {
            List<List<Point2d>> regions = new List<List<Point2d>>();

            // 收集所有水平分割点
            List<double> splitPoints = new List<double> { panelMinY, panelMaxY };

            foreach (var hole in holes)
            {
                // 检查洞口是否与板材有交集
                if (hole.MaxPoint.X > panelMinX + TOLERANCE && hole.MinPoint.X < panelMaxX - TOLERANCE &&
                    hole.MaxPoint.Y > panelMinY + TOLERANCE && hole.MinPoint.Y < panelMaxY - TOLERANCE)
                {
                    if (hole.MinPoint.Y > panelMinY + TOLERANCE && hole.MinPoint.Y < panelMaxY - TOLERANCE)
                        splitPoints.Add(hole.MinPoint.Y);
                    if (hole.MaxPoint.Y > panelMinY + TOLERANCE && hole.MaxPoint.Y < panelMaxY - TOLERANCE)
                        splitPoints.Add(hole.MaxPoint.Y);
                }
            }

            // 排序并去重
            splitPoints.Sort();
            splitPoints = splitPoints.Distinct().ToList();

            // 生成水平条带区域
            for (int i = 0; i < splitPoints.Count - 1; i++)
            {
                double stripMinY = splitPoints[i];
                double stripMaxY = splitPoints[i + 1];

                if (stripMaxY - stripMinY < MIN_PANEL_HEIGHT)
                    continue;

                // 检查这个条带是否与任何洞口冲突
                bool hasConflict = false;
                foreach (var hole in holes)
                {
                    if (IsHorizontalStripConflictWithHole(panelMinX, panelMaxX, stripMinY, stripMaxY, hole))
                    {
                        hasConflict = true;
                        break;
                    }
                }

                if (!hasConflict)
                {
                    // 创建有效的矩形区域
                    List<Point2d> region = new List<Point2d>
                    {
                        new Point2d(panelMinX, stripMinY),
                        new Point2d(panelMaxX, stripMinY),
                        new Point2d(panelMaxX, stripMaxY),
                        new Point2d(panelMinX, stripMaxY)
                    };
                    regions.Add(region);
                }
            }

            return regions;
        }

        /// <summary>
        /// 检查垂直条带是否与洞口冲突
        /// </summary>
        private bool IsStripConflictWithHole(double stripMinX, double stripMaxX, double stripMinY, double stripMaxY, Extents3d hole)
        {
            // 检查条带与洞口是否有重叠
            return !(stripMaxX <= hole.MinPoint.X + TOLERANCE || stripMinX >= hole.MaxPoint.X - TOLERANCE ||
                     stripMaxY <= hole.MinPoint.Y + TOLERANCE || stripMinY >= hole.MaxPoint.Y - TOLERANCE);
        }

        /// <summary>
        /// 检查水平条带是否与洞口冲突
        /// </summary>
        private bool IsHorizontalStripConflictWithHole(double stripMinX, double stripMaxX, double stripMinY, double stripMaxY, Extents3d hole)
        {
            // 检查条带与洞口是否有重叠
            return !(stripMaxX <= hole.MinPoint.X + TOLERANCE || stripMinX >= hole.MaxPoint.X - TOLERANCE ||
                     stripMaxY <= hole.MinPoint.Y + TOLERANCE || stripMinY >= hole.MaxPoint.Y - TOLERANCE);
        }

        /// <summary>
        /// 为有冲突的条带生成复杂区域（如L形）
        /// </summary>
        private List<List<Point2d>> GenerateComplexRegionForStrip(double stripMinX, double stripMaxX,
            double stripMinY, double stripMaxY, List<Extents3d> holes)
        {
            List<List<Point2d>> regions = new List<List<Point2d>>();

            // 找到与此条带相交的洞口
            List<Extents3d> intersectingHoles = new List<Extents3d>();
            foreach (var hole in holes)
            {
                if (IsStripConflictWithHole(stripMinX, stripMaxX, stripMinY, stripMaxY, hole))
                {
                    intersectingHoles.Add(hole);
                }
            }

            if (intersectingHoles.Count == 0)
                return regions;

            // 简化处理：为每个洞口生成上下两个矩形区域
            foreach (var hole in intersectingHoles)
            {
                // 洞口下方区域
                if (hole.MinPoint.Y > stripMinY + MIN_PANEL_HEIGHT)
                {
                    List<Point2d> bottomRegion = new List<Point2d>
                    {
                        new Point2d(stripMinX, stripMinY),
                        new Point2d(stripMaxX, stripMinY),
                        new Point2d(stripMaxX, hole.MinPoint.Y),
                        new Point2d(stripMinX, hole.MinPoint.Y)
                    };
                    regions.Add(bottomRegion);
                }

                // 洞口上方区域
                if (hole.MaxPoint.Y < stripMaxY - MIN_PANEL_HEIGHT)
                {
                    List<Point2d> topRegion = new List<Point2d>
                    {
                        new Point2d(stripMinX, hole.MaxPoint.Y),
                        new Point2d(stripMaxX, hole.MaxPoint.Y),
                        new Point2d(stripMaxX, stripMaxY),
                        new Point2d(stripMinX, stripMaxY)
                    };
                    regions.Add(topRegion);
                }
            }

            return regions;
        }

        /// <summary>
        /// 计算多边形面积
        /// </summary>
        private double CalculatePolygonArea(List<Point2d> points)
        {
            if (points == null || points.Count < 3)
                return 0;

            double area = 0;
            int n = points.Count;

            for (int i = 0; i < n; i++)
            {
                int j = (i + 1) % n;
                area += points[i].X * points[j].Y;
                area -= points[j].X * points[i].Y;
            }

            return Math.Abs(area) / 2.0;
        }

        /// <summary>
        /// 使用传统方法生成板材（作为智能算法的备选方案）
        /// </summary>
        private int GeneratePanelUsingTraditionalMethod(double panelMinX, double panelMaxX, double minY, double topY,
            List<Extents3d> relevantWindows, List<Extents3d> relevantDoors, BlockTableRecord ms, Transaction tr)
        {
            int panelCount = 0;

            // 检查是否需要对板进行分段(遇到门窗)
            List<Tuple<double, double>> yRanges = new List<Tuple<double, double>>();
            yRanges.Add(new Tuple<double, double>(minY, topY));

            // 处理窗户
            foreach (Extents3d window in relevantWindows)
            {
                // 判断板是否在洞口范围内(水平方向)
                if (IsRectCompletelyInsideHorizontally(panelMinX, panelMaxX, window.MinPoint.X, window.MaxPoint.X))
                {
                    // 分割Y范围，避开窗户，并加上间隙
                    SplitYRanges(ref yRanges, window.MinPoint.Y - _windowGap, window.MaxPoint.Y + _windowGap);
                }
            }

            // 处理门
            foreach (Extents3d door in relevantDoors)
            {
                // 判断板是否在洞口范围内(水平方向)
                if (IsRectCompletelyInsideHorizontally(panelMinX, panelMaxX, door.MinPoint.X, door.MaxPoint.X))
                {
                    // 门只有顶部间隙，底部到地面
                    SplitYRanges(ref yRanges, minY, door.MaxPoint.Y + _doorTopGap);
                }
            }

            // 为每个Y范围创建矩形板
            foreach (var range in yRanges)
            {
                // 确保高度足够
                if (range.Item2 - range.Item1 > MIN_PANEL_HEIGHT)
                {
                    try
                    {
                        Point2d pt1 = new Point2d(panelMinX, range.Item1);
                        Point2d pt2 = new Point2d(panelMaxX, range.Item1);
                        Point2d pt3 = new Point2d(panelMaxX, range.Item2);
                        Point2d pt4 = new Point2d(panelMinX, range.Item2);

                        // 创建矩形板
                        using (Polyline panel = new Polyline())
                        {
                            panel.AddVertexAt(0, pt1, 0, 0, 0);
                            panel.AddVertexAt(1, pt2, 0, 0, 0);
                            panel.AddVertexAt(2, pt3, 0, 0, 0);
                            panel.AddVertexAt(3, pt4, 0, 0, 0);
                            panel.Closed = true;

                            // 将板添加到模型空间
                            ms.AppendEntity(panel);
                            tr.AddNewlyCreatedDBObject(panel, true);
                            panelCount++;
                        }
                    }
                    catch (System.Exception ex)
                    {
                        _ed.WriteMessage($"\n创建传统矩形板时出错: {ex.Message}");
                    }
                }
            }

            return panelCount;
        }

        /// <summary>
        /// 获取与指定X范围相交的洞口
        /// </summary>
        /// <param name="allExtents">所有洞口范围</param>
        /// <param name="minX">X范围最小值</param>
        /// <param name="maxX">X范围最大值</param>
        /// <returns>相交的洞口范围列表</returns>
        private List<Extents3d> GetIntersectingHoles(List<Extents3d> allExtents, double minX, double maxX)
        {
            List<Extents3d> intersecting = new List<Extents3d>();

            foreach (var extent in allExtents)
            {
                // 检查X方向是否有重叠
                if (!(extent.MaxPoint.X < minX - TOLERANCE || extent.MinPoint.X > maxX + TOLERANCE))
                {
                    intersecting.Add(extent);
                }
            }

            return intersecting;
        }

        /// <summary>
        /// 获取指定X范围内轮廓线的最低Y坐标（用于水平顶部矩形板）
        /// </summary>
        private double GetOutlineLowestYAtXrange(Polyline outline, double minX, double maxX)
        {
            double lowestY = double.MaxValue;
            bool foundPoint = false;

            // 检查每个线段与给定X范围的交点
            for (int i = 0; i < outline.NumberOfVertices; i++)
            {
                int nextIdx = (i + 1) % outline.NumberOfVertices;
                Point2d p1 = outline.GetPoint2dAt(i);
                Point2d p2 = outline.GetPoint2dAt(nextIdx);

                // 判断线段是否在X范围内
                if ((p1.X >= minX && p1.X <= maxX) || (p2.X >= minX && p2.X <= maxX) ||
                    (p1.X <= minX && p2.X >= maxX) || (p1.X >= minX && p2.X <= maxX))
                {
                    // 如果线段端点在X范围内，检查Y坐标
                    if (p1.X >= minX && p1.X <= maxX)
                    {
                        lowestY = Math.Min(lowestY, p1.Y);
                        foundPoint = true;
                    }
                    
                    if (p2.X >= minX && p2.X <= maxX)
                    {
                        lowestY = Math.Min(lowestY, p2.Y);
                        foundPoint = true;
                    }

                    // 如果线段与X范围的边界相交，计算交点的Y值
                    if ((p1.X < minX && p2.X > minX) || (p1.X > minX && p2.X < minX))
                    {
                        // 计算与minX的交点
                        double t = (minX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        lowestY = Math.Min(lowestY, y);
                        foundPoint = true;
                    }
                    
                    if ((p1.X < maxX && p2.X > maxX) || (p1.X > maxX && p2.X < maxX))
                    {
                        // 计算与maxX的交点
                        double t = (maxX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        lowestY = Math.Min(lowestY, y);
                        foundPoint = true;
                    }
                }
            }

            if (foundPoint)
            {
                return lowestY;
            }
            
            // 如果没有找到点，返回一个默认值
            return double.MaxValue;
        }

        /// <summary>
        /// 获取指定X范围内轮廓线的最高Y坐标（用于确保矩形板顶部水平且不超出轮廓线）
        /// </summary>
        private double GetOutlineHighestYAtXrange(Polyline outline, double minX, double maxX)
        {
            double highestY = double.MinValue;
            bool foundPoint = false;

            // 检查每个线段与给定X范围的交点
            for (int i = 0; i < outline.NumberOfVertices; i++)
            {
                int nextIdx = (i + 1) % outline.NumberOfVertices;
                Point2d p1 = outline.GetPoint2dAt(i);
                Point2d p2 = outline.GetPoint2dAt(nextIdx);

                // 判断线段是否在X范围内
                if ((p1.X >= minX && p1.X <= maxX) || (p2.X >= minX && p2.X <= maxX) ||
                    (p1.X <= minX && p2.X >= maxX) || (p1.X >= minX && p2.X <= maxX))
                {
                    // 如果线段端点在X范围内，检查Y坐标
                    if (p1.X >= minX && p1.X <= maxX)
                    {
                        highestY = Math.Max(highestY, p1.Y);
                        foundPoint = true;
                    }
                    
                    if (p2.X >= minX && p2.X <= maxX)
                    {
                        highestY = Math.Max(highestY, p2.Y);
                        foundPoint = true;
                    }

                    // 如果线段与X范围的边界相交，计算交点的Y值
                    if ((p1.X < minX && p2.X > minX) || (p1.X > minX && p2.X < minX))
                    {
                        // 计算与minX的交点
                        double t = (minX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        highestY = Math.Max(highestY, y);
                        foundPoint = true;
                    }
                    
                    if ((p1.X < maxX && p2.X > maxX) || (p1.X > maxX && p2.X < maxX))
                    {
                        // 计算与maxX的交点
                        double t = (maxX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        highestY = Math.Max(highestY, y);
                        foundPoint = true;
                    }
                }
            }

            if (foundPoint)
            {
                return highestY;
            }
            
            // 如果没有找到点，返回一个默认值
            return double.MinValue;
        }

        /// <summary>
        /// 获取轮廓线在指定X坐标处的Y坐标（取最高点）
        /// </summary>
        private double GetYCoordinateAtX(Polyline outline, double x)
        {
            try
            {
                double highestY = double.MinValue;
                bool foundPoint = false;

                // 检查每个线段
                for (int i = 0; i < outline.NumberOfVertices; i++)
                {
                    int nextIdx = (i + 1) % outline.NumberOfVertices;
                    Point2d p1 = outline.GetPoint2dAt(i);
                    Point2d p2 = outline.GetPoint2dAt(nextIdx);

                    // 判断线段是否与X坐标相交（使用容差比较）
                    bool p1InRange = Math.Abs(p1.X - x) < TOLERANCE;
                    bool p2InRange = Math.Abs(p2.X - x) < TOLERANCE;
                    bool lineSpansX = (p1.X <= x && p2.X >= x) || (p1.X >= x && p2.X <= x);

                    if (p1InRange || p2InRange || lineSpansX)
                    {
                        // 如果线段端点正好在X坐标处
                        if (p1InRange)
                        {
                            highestY = Math.Max(highestY, p1.Y);
                            foundPoint = true;
                        }

                        if (p2InRange)
                        {
                            highestY = Math.Max(highestY, p2.Y);
                            foundPoint = true;
                        }

                        // 如果线段跨越X坐标且不是垂直线
                        if (!p1InRange && !p2InRange && Math.Abs(p2.X - p1.X) > TOLERANCE)
                        {
                            // 计算交点的Y值
                            double t = (x - p1.X) / (p2.X - p1.X);

                            // 确保t在有效范围内
                            if (t >= -TOLERANCE && t <= 1.0 + TOLERANCE)
                            {
                                double y = p1.Y + t * (p2.Y - p1.Y);
                                highestY = Math.Max(highestY, y);
                                foundPoint = true;
                            }
                        }
                    }
                }

                if (foundPoint)
                {
                    return highestY;
                }

                // 如果没有找到交点，返回默认值
                return double.MinValue;
            }
            catch (Exception ex)
            {
                throw new GeometryCalculationException($"获取X坐标{x}处的Y坐标时发生错误", ex);
            }
        }



        /// <summary>
        /// 添加窗洞口
        /// </summary>
        /// <returns>是否成功添加窗洞口</returns>
        public bool AddWindow()
        {
            // 创建选择选项
            PromptSelectionOptions selOptions = new PromptSelectionOptions();
            selOptions.MessageForAdding = "\n选择窗洞口(闭合多段线): ";
            selOptions.AllowDuplicates = false;

            // 创建过滤器，只选择多段线
            TypedValue[] filterList = new TypedValue[1];
            filterList[0] = new TypedValue((int)DxfCode.Start, "LWPOLYLINE");
            SelectionFilter filter = new SelectionFilter(filterList);

            try
            {
                // 提示用户选择对象
                PromptSelectionResult selResult = _ed.GetSelection(selOptions, filter);
                if (selResult.Status != PromptStatus.OK)
                    return false;

                // 处理选中的对象
                bool addedAny = false;
                using (Transaction tr = _db.TransactionManager.StartTransaction())
                {
                    try
                    {
                        SelectionSet ss = selResult.Value;
                        foreach (SelectedObject selObj in ss)
                        {
                            if (selObj != null)
                            {
                                Polyline pline = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Polyline;
                                if (pline != null && pline.Closed && pline.NumberOfVertices == 4)
                                {
                                    _windowIds.Add(selObj.ObjectId);
                                    addedAny = true;
                                }
                                else
                                {
                                    _ed.WriteMessage("\n忽略非闭合或非四边形的多段线。");
                                }
                            }
                        }
                        tr.Commit();
                    }
                    catch (System.Exception ex)
                    {
                        _ed.WriteMessage($"\n添加窗洞口时出错: {ex.Message}");
                        tr.Abort();
                        return false;
                    }
                }

                if (addedAny)
                {
                    _ed.WriteMessage($"\n已添加{_windowIds.Count}个窗洞口。");
                    return true;
                }
                else
                {
                    _ed.WriteMessage("\n未找到符合条件的窗洞口（需要是闭合的四边形多段线）。");
                    return false;
                }
            }
            finally
            {
                // SelectionFilter不需要手动释放
            }
        }

        /// <summary>
        /// 添加门洞口
        /// </summary>
        /// <returns>是否成功添加门洞口</returns>
        public bool AddDoor()
        {
            // 创建选择选项
            PromptSelectionOptions selOptions = new PromptSelectionOptions();
            selOptions.MessageForAdding = "\n选择门洞口(由三条线组成的多段线): ";
            selOptions.AllowDuplicates = false;

            // 创建过滤器，只选择多段线
            TypedValue[] filterList = new TypedValue[1];
            filterList[0] = new TypedValue((int)DxfCode.Start, "LWPOLYLINE");
            SelectionFilter filter = new SelectionFilter(filterList);

            try
            {
                // 提示用户选择对象
                PromptSelectionResult selResult = _ed.GetSelection(selOptions, filter);
                if (selResult.Status != PromptStatus.OK)
                    return false;

                // 处理选中的对象
                bool addedAny = false;
                using (Transaction tr = _db.TransactionManager.StartTransaction())
                {
                    try
                    {
                        SelectionSet ss = selResult.Value;
                        foreach (SelectedObject selObj in ss)
                        {
                            if (selObj != null)
                            {
                                Polyline pline = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Polyline;
                                if (pline != null && !pline.Closed && pline.NumberOfVertices == 4) // 三条线，四个点(首尾不同)
                                {
                                    _doorIds.Add(selObj.ObjectId);
                                    addedAny = true;
                                }
                                else
                                {
                                    _ed.WriteMessage("\n忽略闭合或非三线段的多段线。");
                                }
                            }
                        }
                        tr.Commit();
                    }
                    catch (System.Exception ex)
                    {
                        _ed.WriteMessage($"\n添加门洞口时出错: {ex.Message}");
                        tr.Abort();
                        return false;
                    }
                }

                if (addedAny)
                {
                    _ed.WriteMessage($"\n已添加{_doorIds.Count}个门洞口。");
                    return true;
                }
                else
                {
                    _ed.WriteMessage("\n未找到符合条件的门洞口（需要是非闭合的三线段多段线）。");
                    return false;
                }
            }
            finally
            {
                // SelectionFilter不需要手动释放
            }
        }

        #region 选择方法
        /// <summary>
        /// 设置轮廓线
        /// </summary>
        /// <returns>是否成功选择轮廓线</returns>
        public bool SetOutline()
        {
            PromptEntityResult result = _ed.GetEntity("\n请选择轮廓线: ");
            if (result.Status != PromptStatus.OK)
                return false;

            using (Transaction tr = _db.TransactionManager.StartTransaction())
            {
                try
                {
                    Polyline outline = tr.GetObject(result.ObjectId, OpenMode.ForRead) as Polyline;
                    if (outline == null)
                    {
                        _ed.WriteMessage("\n请选择有效的多段线!");
                        tr.Abort();
                        return false;
                    }

                    // 存储ObjectId而不是直接引用对象
                    _outlineId = result.ObjectId;
                    tr.Commit();
                    return true;
                }
                catch (System.Exception ex)
                {
                    _ed.WriteMessage("\n选择轮廓线时出错: {0}", ex.Message);
                    tr.Abort();
                    return false;
                }
            }
        }

        /// <summary>
        /// 清除选择的门窗和轮廓
        /// </summary>
        public void ClearSelection()
        {
            _outlineId = ObjectId.Null;
            _windowIds.Clear();
            _doorIds.Clear();
        }
        #endregion
        #endregion
    }
}