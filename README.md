# 墙面矩形板竖向排板工具 (RectangularPanelTool)

## 项目概述

该工具是一个专业的AutoCAD插件，用于自动化生成墙面矩形板的竖向排版。通过智能的几何计算和用户友好的界面，可以大幅提高建筑设计中墙面板材排版的效率和准确性。

## 版本信息

- **版本**: 2.0 (重构版本)
- **发布日期**: 2024年
- **兼容性**: AutoCAD 2023+, .NET Framework 4.8

## 主要功能

### 1. 智能轮廓线处理
- 支持任意形状的闭合/开放多段线作为墙面轮廓线
- 自动闭合开放轮廓线进行处理
- 复杂几何形状的精确边界计算

### 2. 门窗洞口智能识别
- **窗洞口**: 四条线组成的闭合多段线，支持上下间隙设置
- **门洞口**: 三条线组成的开放多段线（左、右、上），支持顶部间隙设置
- 自动验证洞口几何有效性

### 3. 多种排版方向
- **从左往右排版**: 从轮廓线左边界开始排列
- **从右往左排版**: 从轮廓线右边界开始排列
- **从中间向两边排版**: 用户指定中心点，向两边同时排列

### 4. 智能避让算法
- 矩形板遇到门窗洞口时自动分段
- 仅当板宽完全在洞口范围内才进行分段
- 支持自定义间隙距离设置
- 最小板高度限制，避免生成过小的板材

### 5. 参数化设置
- 排版起始距离（支持负值向外扩展）
- 矩形板宽度
- 板与轮廓线顶部/底部间隙
- 窗户上下口间隙
- 门顶部间隙
- 实时参数验证

## 技术特性

### 代码质量改进 (v2.0)
- ✅ **内存管理**: 修复内存泄漏，使用ObjectId替代直接对象引用
- ✅ **异常处理**: 完善的分层异常处理机制
- ✅ **参数验证**: 严格的输入验证和边界条件检查
- ✅ **性能优化**: 空间索引优化几何查询性能
- ✅ **数值精度**: 浮点数比较使用容差避免精度问题
- ✅ **循环保护**: 防止无限循环的安全机制
- ✅ **事务管理**: 正确的AutoCAD事务生命周期管理

### 架构改进
- 模块化设计，职责分离
- 自定义异常类型
- 统一的UI状态管理
- 代码重复消除

## 使用方法

### 基本操作流程
1. 在AutoCAD中输入命令 `RPTOOL` 启动工具
2. 在弹出的参数设置窗体中：
   - 点击"选择轮廓线"按钮，选择墙面轮廓多段线
   - 点击"选择窗洞口"按钮，选择窗户洞口（可选）
   - 点击"选择门洞口"按钮，选择门洞口（可选）
   - 设置排版参数（起始距离、板宽、各种间隙）
3. 选择排版方向（左到右/右到左/中心向两边）
4. 点击"生成矩形板"按钮完成排版

### 参数说明
- **起始距离**: 排版起始位置距离边界的距离（负值表示向外扩展）
- **板宽**: 每个矩形板的宽度
- **顶部间隙**: 板与轮廓线顶部的间隙
- **底部间隙**: 板与轮廓线底部的间隙
- **窗户间隙**: 板与窗洞口上下的间隙
- **门顶部间隙**: 板与门洞口顶部的间隙

## 系统要求

- **AutoCAD版本**: 2023 或更高版本
- **运行环境**: .NET Framework 4.8
- **操作系统**: Windows 10/11
- **内存**: 建议4GB以上

## 安装和部署

### 方法一：手动安装
1. 编译项目生成 `RectangularPanelTool.dll`
2. 将DLL文件复制到AutoCAD插件目录
3. 在AutoCAD中使用 `NETLOAD` 命令加载DLL
4. 输入 `RPTOOL` 命令启动工具

### 方法二：自动加载
1. 将DLL放置在AutoCAD的自动加载目录
2. 重启AutoCAD，插件将自动加载
3. 直接输入 `RPTOOL` 命令使用

## 开发信息

### 项目结构
```
RectangularPanelTool/
├── Commands.cs              # AutoCAD命令入口
├── PanelForm.cs            # 用户界面窗体
├── PanelForm.Designer.cs   # 窗体设计器代码
├── PanelGenerator.cs       # 核心算法引擎
├── Properties/
│   └── AssemblyInfo.cs     # 程序集信息
├── README.md               # 项目文档
└── RectangularPanelTool.csproj # 项目文件
```

### 核心类说明
- **Commands**: AutoCAD命令注册和入口点
- **PanelForm**: Windows Forms用户界面，参数设置和交互
- **PanelGenerator**: 核心算法实现，几何计算和板材生成

### 异常类型
- **InvalidParameterException**: 参数验证异常
- **GeometryCalculationException**: 几何计算异常

## 故障排除

### 常见问题
1. **无法选择轮廓线**: 确保选择的是多段线(LWPOLYLINE)对象
2. **生成板材数量为0**: 检查轮廓线是否闭合，参数设置是否合理
3. **程序崩溃**: 检查AutoCAD版本兼容性，确保.NET Framework版本正确

### 错误代码
- 详细错误信息会显示在AutoCAD命令行中
- 参数错误会在界面中实时提示
- 几何计算错误会提供具体的错误位置信息

## 更新日志

### v2.0 (2024)
- 🔄 完全重构代码架构
- 🛠️ 修复所有已知的内存泄漏和稳定性问题
- ⚡ 性能优化，支持大量门窗洞口的场景
- 🎯 改进用户界面和交互体验
- 📝 完善异常处理和错误提示
- 🔒 增强参数验证和边界条件处理

### v1.0 (初始版本)
- 基本的排版功能实现
- 支持左右排版方向
- 基础的门窗洞口处理

## 许可证

Copyright © 2024. 保留所有权利。

## 技术支持

如有技术问题或功能建议，请通过以下方式联系：
- 在项目中提交Issue
- 查看AutoCAD命令行的详细错误信息
- 参考本文档的故障排除部分