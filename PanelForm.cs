using System;
using System.Drawing;
using System.Windows.Forms;
using System.Globalization;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using AcAp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace RectangularPanelTool
{
    public partial class PanelForm : Form
    {
        #region 私有字段
        private Document _doc;
        private Editor _ed;
        private PanelGenerator _generator;
        private bool _isProcessing = false;
        #endregion

        #region 构造函数
        public PanelForm(Document doc)
        {
            InitializeComponent();

            _doc = doc ?? throw new ArgumentNullException(nameof(doc));
            _ed = doc.Editor;
            _generator = new PanelGenerator(doc);

            // 设置默认选中的排版方向
            radioLeftToRight.Checked = true;
        }
        #endregion

        #region 事件处理
        private void PanelForm_Load(object sender, EventArgs e)
        {
            try
            {
                // 设置默认值
                txtStartDistance.Text = "0";
                txtPanelWidth.Text = "1000";
                txtTopGap.Text = "5";
                txtBottomGap.Text = "10";
                txtWindowGap.Text = "5";
                txtDoorTopGap.Text = "5";

                // 添加输入验证事件
                AttachValidationEvents();

                UpdateSelectionInfo();
                UpdateUIState();
            }
            catch (System.Exception ex)
            {
                ShowError("初始化窗体时发生错误", ex);
            }
        }

        /// <summary>
        /// 附加输入验证事件
        /// </summary>
        private void AttachValidationEvents()
        {
            txtStartDistance.Leave += ValidateNumericInput;
            txtPanelWidth.Leave += ValidatePositiveNumericInput;
            txtTopGap.Leave += ValidateNonNegativeNumericInput;
            txtBottomGap.Leave += ValidateNonNegativeNumericInput;
            txtWindowGap.Leave += ValidateNonNegativeNumericInput;
            txtDoorTopGap.Leave += ValidateNonNegativeNumericInput;
        }

        /// <summary>
        /// 验证数值输入
        /// </summary>
        private void ValidateNumericInput(object sender, EventArgs e)
        {
            TextBox textBox = sender as TextBox;
            if (textBox != null && !string.IsNullOrWhiteSpace(textBox.Text))
            {
                if (!double.TryParse(textBox.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out _))
                {
                    MessageBox.Show($"请输入有效的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBox.Focus();
                    textBox.SelectAll();
                }
            }
        }

        /// <summary>
        /// 验证正数输入
        /// </summary>
        private void ValidatePositiveNumericInput(object sender, EventArgs e)
        {
            TextBox textBox = sender as TextBox;
            if (textBox != null && !string.IsNullOrWhiteSpace(textBox.Text))
            {
                if (!double.TryParse(textBox.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double value) || value <= 0)
                {
                    MessageBox.Show($"请输入大于0的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBox.Focus();
                    textBox.SelectAll();
                }
            }
        }

        /// <summary>
        /// 验证非负数输入
        /// </summary>
        private void ValidateNonNegativeNumericInput(object sender, EventArgs e)
        {
            TextBox textBox = sender as TextBox;
            if (textBox != null && !string.IsNullOrWhiteSpace(textBox.Text))
            {
                if (!double.TryParse(textBox.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double value) || value < 0)
                {
                    MessageBox.Show($"请输入非负数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBox.Focus();
                    textBox.SelectAll();
                }
            }
        }
        #endregion

        #region 选择事件处理
        private void btnSelectOutline_Click(object sender, EventArgs e)
        {
            HandleSelection(() => _generator.SetOutline(), "未能选择有效的轮廓线", "选择轮廓线");
        }

        private void btnSelectWindow_Click(object sender, EventArgs e)
        {
            HandleSelection(() => _generator.AddWindow(), "未能选择有效的窗洞口", "选择窗洞口");
        }

        private void btnSelectDoor_Click(object sender, EventArgs e)
        {
            HandleSelection(() => _generator.AddDoor(), "未能选择有效的门洞口", "选择门洞口");
        }

        /// <summary>
        /// 通用选择处理方法
        /// </summary>
        /// <param name="selectionAction">选择操作</param>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="operationName">操作名称</param>
        private void HandleSelection(Func<bool> selectionAction, string errorMessage, string operationName)
        {
            if (_isProcessing)
            {
                MessageBox.Show("正在处理中，请稍候...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            _isProcessing = true;
            UpdateUIState();

            try
            {
                // 隐藏窗体以便用户选择对象
                this.Hide();

                bool success = selectionAction();
                if (!success)
                {
                    ShowError(errorMessage, null);
                }
            }
            catch (InvalidParameterException ex)
            {
                ShowError($"{operationName}参数错误", ex);
            }
            catch (System.Exception ex)
            {
                ShowError($"{operationName}时发生错误", ex);
            }
            finally
            {
                this.Show();
                _isProcessing = false;
                UpdateSelectionInfo();
                UpdateUIState();
            }
        }
        #endregion

        #region UI状态管理
        /// <summary>
        /// 更新选择信息显示
        /// </summary>
        private void UpdateSelectionInfo()
        {
            try
            {
                // 更新轮廓线选择状态
                lblOutlineStatus.Text = _generator.HasOutline ? "已选择" : "未选择";
                lblOutlineStatus.ForeColor = _generator.HasOutline ? Color.Green : Color.Red;

                // 更新窗洞口选择状态
                if (_generator.WindowCount > 0)
                {
                    lblWindowStatus.Text = $"已选择窗洞口: {_generator.WindowCount}个";
                    lblWindowStatus.ForeColor = Color.Green;
                }
                else
                {
                    lblWindowStatus.Text = "未选择";
                    lblWindowStatus.ForeColor = Color.Gray;
                }

                // 更新门洞口选择状态
                if (_generator.DoorCount > 0)
                {
                    lblDoorStatus.Text = $"已选择门洞口: {_generator.DoorCount}个";
                    lblDoorStatus.ForeColor = Color.Green;
                }
                else
                {
                    lblDoorStatus.Text = "未选择";
                    lblDoorStatus.ForeColor = Color.Gray;
                }
            }
            catch (System.Exception ex)
            {
                ShowError("更新选择信息时发生错误", ex);
            }
        }

        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUIState()
        {
            bool canGenerate = _generator.HasOutline && !_isProcessing;

            btnGenerate.Enabled = canGenerate;
            btnSelectOutline.Enabled = !_isProcessing;
            btnSelectWindow.Enabled = !_isProcessing;
            btnSelectDoor.Enabled = !_isProcessing;

            // 更新按钮文本
            if (_isProcessing)
            {
                btnGenerate.Text = "处理中...";
            }
            else
            {
                btnGenerate.Text = "生成矩形板";
            }
        }

        /// <summary>
        /// 显示错误信息
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="ex">异常对象</param>
        private void ShowError(string message, System.Exception ex)
        {
            string fullMessage = message;
            if (ex != null)
            {
                fullMessage += $"\n详细信息: {ex.Message}";

                // 记录到AutoCAD命令行
                _ed?.WriteMessage($"\n错误: {message} - {ex.Message}");
            }

            MessageBox.Show(fullMessage, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        /// <summary>
        /// 显示成功信息
        /// </summary>
        /// <param name="message">成功消息</param>
        private void ShowSuccess(string message)
        {
            MessageBox.Show(message, "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            _ed?.WriteMessage($"\n{message}");
        }
        #endregion

        #region 生成处理
        /// <summary>
        /// 验证并应用参数设置
        /// </summary>
        /// <returns>是否验证成功</returns>
        private bool ValidateAndApplyParameters()
        {
            try
            {
                // 验证并设置起始距离
                if (!double.TryParse(txtStartDistance.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double startDistance))
                {
                    ShowError("起始距离必须是有效的数值", null);
                    txtStartDistance.Focus();
                    return false;
                }
                _generator.StartDistance = startDistance;

                // 验证并设置板宽
                if (!double.TryParse(txtPanelWidth.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double panelWidth) || panelWidth <= 0)
                {
                    ShowError("板宽必须是大于0的数值", null);
                    txtPanelWidth.Focus();
                    return false;
                }
                _generator.PanelWidth = panelWidth;

                // 验证并设置顶部间隙
                if (!double.TryParse(txtTopGap.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double topGap) || topGap < 0)
                {
                    ShowError("顶部间隙必须是非负数值", null);
                    txtTopGap.Focus();
                    return false;
                }
                _generator.TopGap = topGap;

                // 验证并设置底部间隙
                if (!double.TryParse(txtBottomGap.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double bottomGap) || bottomGap < 0)
                {
                    ShowError("底部间隙必须是非负数值", null);
                    txtBottomGap.Focus();
                    return false;
                }
                _generator.BottomGap = bottomGap;

                // 验证并设置窗户间隙
                if (!double.TryParse(txtWindowGap.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double windowGap) || windowGap < 0)
                {
                    ShowError("窗户间隙必须是非负数值", null);
                    txtWindowGap.Focus();
                    return false;
                }
                _generator.WindowGap = windowGap;

                // 验证并设置门顶部间隙
                if (!double.TryParse(txtDoorTopGap.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double doorTopGap) || doorTopGap < 0)
                {
                    ShowError("门顶部间隙必须是非负数值", null);
                    txtDoorTopGap.Focus();
                    return false;
                }
                _generator.DoorTopGap = doorTopGap;

                return true;
            }
            catch (InvalidParameterException ex)
            {
                ShowError("参数设置错误", ex);
                return false;
            }
            catch (System.Exception ex)
            {
                ShowError("验证参数时发生错误", ex);
                return false;
            }
        }

        private void GeneratePanels(PanelDirection direction)
        {
            if (_isProcessing)
            {
                return;
            }

            if (!_generator.HasOutline)
            {
                ShowError("请先选择轮廓线", null);
                return;
            }

            // 验证并应用参数设置
            if (!ValidateAndApplyParameters())
            {
                return;
            }

            _generator.Direction = direction;
            _isProcessing = true;
            UpdateUIState();

            try
            {
                // 对于中心向两边排版，不需要隐藏窗体（用户交互已移到事务外）
                if (direction != PanelDirection.CenterToSides)
                {
                    this.Hide();
                }

                // 生成矩形板
                int count = _generator.GeneratePanels();

                if (count > 0)
                {
                    ShowSuccess($"成功生成{count}个矩形板");
                    // 生成成功，关闭窗体
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                    return;
                }
                else
                {
                    ShowError("未能生成任何矩形板，请检查参数设置和轮廓线", null);
                }
            }
            catch (InvalidParameterException ex)
            {
                ShowError("参数错误", ex);
            }
            catch (GeometryCalculationException ex)
            {
                ShowError("几何计算错误", ex);
            }
            catch (System.Exception ex)
            {
                ShowError("生成矩形板时发生错误", ex);
            }
            finally
            {
                if (direction != PanelDirection.CenterToSides)
                {
                    this.Show();
                }
                _isProcessing = false;
                UpdateUIState();
            }
        }
        #endregion

        #region 按钮事件处理
        private void btnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                // 清理资源
                _generator?.ClearSelection();
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (System.Exception ex)
            {
                ShowError("关闭窗体时发生错误", ex);
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }

        private void btnGenerate_Click(object sender, EventArgs e)
        {
            try
            {
                // 根据选中的单选按钮决定排版方向
                PanelDirection direction = GetSelectedDirection();
                GeneratePanels(direction);
            }
            catch (System.Exception ex)
            {
                ShowError("启动生成过程时发生错误", ex);
            }
        }

        /// <summary>
        /// 获取选中的排版方向
        /// </summary>
        /// <returns>排版方向</returns>
        private PanelDirection GetSelectedDirection()
        {
            if (radioRightToLeft.Checked)
                return PanelDirection.RightToLeft;
            else if (radioCenterToSides.Checked)
                return PanelDirection.CenterToSides;
            else
                return PanelDirection.LeftToRight; // 默认值
        }
        #endregion

        #region 窗体生命周期
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                // 清理资源
                _generator?.ClearSelection();
            }
            catch (System.Exception ex)
            {
                _ed?.WriteMessage($"\n清理资源时发生错误: {ex.Message}");
            }

            base.OnFormClosing(e);
        }
        #endregion
    }
} 