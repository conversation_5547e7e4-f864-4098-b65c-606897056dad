using System;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;

[assembly: CommandClass(typeof(RectangularPanelTool.Commands))]

namespace RectangularPanelTool
{
    /// <summary>
    /// AutoCAD命令类
    /// </summary>
    public class Commands
    {
        /// <summary>
        /// 启动墙面矩形板竖向排板工具
        /// </summary>
        [CommandMethod("RPTOOL", CommandFlags.Modal)]
        public void RectangularPanelTool()
        {
            // 获取当前文档和数据库
            Document doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null)
            {
                return;
            }

            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                ed.WriteMessage("\n启动墙面矩形板竖向排板工具...");

                // 创建并显示排板工具窗体
                using (PanelForm form = new PanelForm(doc))
                {
                    var result = Application.ShowModalDialog(form);

                    if (result == System.Windows.Forms.DialogResult.OK)
                    {
                        ed.WriteMessage("\n排板工具执行完成。");
                    }
                    else
                    {
                        ed.WriteMessage("\n排板工具已取消。");
                    }
                }
            }
            catch (InvalidParameterException ex)
            {
                ed.WriteMessage($"\n参数错误: {ex.Message}");
            }
            catch (GeometryCalculationException ex)
            {
                ed.WriteMessage($"\n几何计算错误: {ex.Message}");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n启动排板工具时发生错误: {ex.Message}");

                // 记录详细错误信息用于调试
                ed.WriteMessage($"\n详细错误信息: {ex}");
            }
        }
    }
}